# Chart library: https://github.com/life-time-inc/helm-charts.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
image:
  repository: ghcr.io/life-time-inc/ai-members-scheduling-agent
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets:
  - name: gh-actions-secret

labels:
  # additional labels
  domain: ai
  tags.datadoghq.com/env: "QA"
  tags.datadoghq.com/version: "latest"
  tags.datadoghq.com/service: "ai-members-scheduling-agent"
  admission.datadoghq.com/enabled: "true"
  admission.datadoghq.com/config.mode: "socket"

# use specified agentpool
nodeSelector:
  kubernetes.azure.com/mode: user

# Tolerations are applied to pods, and allow (but do not require) the pods to schedule onto nodes with matching taints.
tolerations:
- key: virtual-kubelet.io/provider
  operator: Exists
- key: "hostname"
  operator: "Equal"
  value: "ai"
  effect: "NoSchedule"

serviceAccount:
  # Specifies whether a service account should be created
  create: false

service:
  # "none" indicates a daemon. Valid values are NodePort, ClusterIP, LoadBalancer, none
  type: ClusterIP
  port: 3000
  containerPort: 3000
  annotations:
    ingress.kubernetes.io/service-upstream: "true"
    admission.datadoghq.com/python-lib.version: v2.11.1

livenessProbe:
# API
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 60
  periodSeconds: 3

readinessProbe:
# API
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 50
  periodSeconds: 3

resources:
  requests:
    cpu: 20m
    memory: 4G
  limits:
    memory: 4G

envFrom:
- configMapRef:
    name: ai-members-scheduling-agent
- secretRef:
    name: ai-members-scheduling-agent

env:
  - name: DD_AGENT_HOST
    valueFrom:
      fieldRef:
        fieldPath: status.hostIP
  - name: DD_ENV
    valueFrom:
      fieldRef:
        fieldPath: metadata.labels['tags.datadoghq.com/env']
  - name: DD_SERVICE
    valueFrom:
      fieldRef:
        fieldPath: metadata.labels['tags.datadoghq.com/service']
  - name: DD_VERSION
    valueFrom:
      fieldRef:
        fieldPath: metadata.labels['tags.datadoghq.com/version']
  - name: DD_LOGS_INJECTION
    value: "true"

replicaSet: 1

# Enable auto scaling ans set min / max expectations
autoscaling:
  enabled: false

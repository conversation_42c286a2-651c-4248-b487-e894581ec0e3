from unittest.mock import patch

from app.handlers.pickleball_handler import format_pickleball_references


class TestPickleballHandler:
    @patch("app.handlers.pickleball_handler.Reference")
    @patch("app.handlers.pickleball_handler.CarouselCard")
    def test_format_pickleball_references_formatting(self, mock_carousel_card, mock_reference):
        """Test that the description is formatted as MM/DD, H:MMam/pm Location"""
        # Create sample references
        references = [
            {
                "label": "Indoor Pickleball Court 1, 2025-04-16, 5:00pm",
                "link": "lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
                "linkType": "deeplink",
                "location_name": "Chanhassen Pickleball Complex",
            }
        ]

        # Setup mocks
        mock_reference.return_value.model_dump.return_value = {}
        mock_carousel_card.return_value.dict.return_value = {}

        # Call the function
        format_pickleball_references(references)

        # Check that CarouselCard was called with the correct arguments
        mock_carousel_card.assert_called_with(
            component="carousel_card",
            imageUrl="https://lifetime.life/content/dam/ltp/images/omsc/sky-pickleball-xl.jpg",
            eyebrowText="<PERSON>hassen Pickleball Complex",
            headlineText="Indoor Pickleball Court 1",
            description="04/16, 5:00pm Chanhassen Pickleball Complex",
            link="lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
            linkType="deeplink",
            cardSize="large",
            name="Chanhassen Pickleball Complex",
        )

    @patch("app.handlers.pickleball_handler.Reference")
    @patch("app.handlers.pickleball_handler.CarouselCard")
    def test_format_pickleball_references_multiple_locations(
        self, mock_carousel_card, mock_reference
    ):
        """Test that references from multiple locations are grouped correctly"""
        # Create sample references from multiple locations
        references = [
            {
                "label": "Indoor Pickleball Court 1, 2025-04-16, 5:00pm",
                "link": "lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
                "linkType": "deeplink",
                "location_name": "Chanhassen Pickleball Complex",
            },
            {
                "label": "Outdoor Pickleball Court 2, 2025-04-16, 6:00pm",
                "link": "lifetime-member://reservecourt?resourcetype=pickleball&clubid=124&courtid=457&time=2025-04-16T18:00:00",
                "linkType": "deeplink",
                "location_name": "Eden Prairie Pickleball Complex",
            },
        ]

        # Setup mocks
        mock_reference.return_value.model_dump.return_value = {}
        mock_carousel_card.return_value.dict.return_value = {}

        # Call the function
        format_pickleball_references(references)

        # Check that CarouselCard was called twice (once for each location)
        assert mock_carousel_card.call_count == 2

        # Get the descriptions from the calls
        call_args_list = mock_carousel_card.call_args_list
        descriptions = [call_args[1]["description"] for call_args in call_args_list]

        # Check that the descriptions are formatted correctly
        assert "04/16, 5:00pm Chanhassen Pickleball Complex" in descriptions
        assert "04/16, 6:00pm Eden Prairie Pickleball Complex" in descriptions

    @patch("app.handlers.pickleball_handler.Reference")
    @patch("app.handlers.pickleball_handler.CarouselCard")
    def test_format_pickleball_references_invalid_date_format(
        self, mock_carousel_card, mock_reference
    ):
        """Test handling of invalid date format in label"""
        # Create sample references with invalid date format
        references = [
            {
                "label": "Indoor Pickleball Court 1, Invalid Date, 5:00pm",
                "link": "lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
                "linkType": "deeplink",
                "location_name": "Chanhassen Pickleball Complex",
            }
        ]

        # Setup mocks
        mock_reference.return_value.model_dump.return_value = {}
        mock_carousel_card.return_value.dict.return_value = {}

        # Call the function
        format_pickleball_references(references)

        # Check that CarouselCard was called with the correct arguments
        mock_carousel_card.assert_called_with(
            component="carousel_card",
            imageUrl="https://lifetime.life/content/dam/ltp/images/omsc/sky-pickleball-xl.jpg",
            eyebrowText="Chanhassen Pickleball Complex",
            headlineText="Indoor Pickleball Court 1",
            description="Invalid Date, 5:00pm Chanhassen Pickleball Complex",
            link="lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
            linkType="deeplink",
            cardSize="large",
            name="Chanhassen Pickleball Complex",
        )

    @patch("app.handlers.pickleball_handler.Reference")
    @patch("app.handlers.pickleball_handler.CarouselCard")
    def test_format_pickleball_references_missing_date_or_time(
        self, mock_carousel_card, mock_reference
    ):
        """Test handling of missing date or time in label"""
        # Create sample references with missing date and time
        references = [
            {
                "label": "Indoor Pickleball Court 1",
                "link": "lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
                "linkType": "deeplink",
                "location_name": "Chanhassen Pickleball Complex",
            }
        ]

        # Setup mocks
        mock_reference.return_value.model_dump.return_value = {}
        mock_carousel_card.return_value.dict.return_value = {}

        # Call the function
        format_pickleball_references(references)

        # Check that CarouselCard was called with the correct arguments
        mock_carousel_card.assert_called_with(
            component="carousel_card",
            imageUrl="https://lifetime.life/content/dam/ltp/images/omsc/sky-pickleball-xl.jpg",
            eyebrowText="Chanhassen Pickleball Complex",
            headlineText="Indoor Pickleball Court 1",
            description="Chanhassen Pickleball Complex",
            link="lifetime-member://reservecourt?resourcetype=pickleball&clubid=123&courtid=456&time=2025-04-16T17:00:00",
            linkType="deeplink",
            cardSize="large",
            name="Chanhassen Pickleball Complex",
        )

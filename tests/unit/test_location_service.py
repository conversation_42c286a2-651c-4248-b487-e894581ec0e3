import os
from unittest.mock import AsyncM<PERSON>, Mock, patch

import httpx
import pytest

from app.models.location import LocationDetail
from app.services.location_service import LocationService


@pytest.fixture
def location_service():
    """Create a LocationService instance for testing."""
    return LocationService()


@pytest.mark.asyncio
async def test_location_detail_model():
    """Test LocationDetail model creation."""
    mock_data = {"location": {"clubTitle": "Life Time Chanhassen", "clubId": "312"}}

    result = LocationDetail(**mock_data)
    assert result is not None
    assert result.location is not None
    assert result.location.clubTitle == "Life Time Chanhassen"
    assert result.location.clubId == "312"


@pytest.mark.asyncio
async def test_get_location_detail_http_error(location_service):
    """Test handling of HTTP errors."""
    with patch("httpx.AsyncClient") as mock_client_class:
        mock_client = AsyncMock()
        mock_response = Mock()  # Use regular Mock for response since raise_for_status is sync
        mock_response.raise_for_status.side_effect = httpx.HTTPError("Not found")

        mock_client.get = AsyncMock(return_value=mock_response)
        mock_client_class.return_value.__aenter__.return_value = mock_client

        result = await location_service.get_location_detail("999")

        assert result is None


@pytest.mark.asyncio
async def test_get_club_locations_multiple_clubs(location_service):
    """Test fetching multiple club locations."""
    mock_responses = {
        "312": {"location": {"clubTitle": "Life Time Chanhassen", "clubId": "312"}},
        "313": {"location": {"clubTitle": "Life Time Eden Prairie", "clubId": "313"}},
    }

    async def mock_get_location_detail(club_id):
        if club_id in mock_responses:
            return LocationDetail(**mock_responses[club_id])
        return None

    with patch.object(
        location_service, "get_location_detail", new=AsyncMock(side_effect=mock_get_location_detail)
    ):
        result = await location_service.get_club_locations(["312", "313"])

        assert len(result) == 2
        assert result["312"] == "Life Time Chanhassen"
        assert result["313"] == "Life Time Eden Prairie"


@pytest.mark.asyncio
async def test_get_club_locations_missing_api_key():
    """Test behavior when API key is missing."""
    with patch.dict(os.environ, {}, clear=True):
        service = LocationService()
        result = await service.get_location_detail("312")
        assert result is None

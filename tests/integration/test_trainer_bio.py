"""
Integration tests for trainer bio functionality.

These tests verify the end-to-end integration between components with minimal mocking.
They test real HTTP calls to external APIs where possible and safe.
"""
import os
from unittest.mock import patch

import pytest
from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

# Test data for integration tests
DEFAULT_SCRATCH_PAD = {
    "name": "Test User",
    "conversationId": "integration-test-123",
    "partyId": 12345,
    "memberId": 67890,
    "homeClub": "312",  # Use a real club ID that exists
    "homeClubName": "Life Time Chanhassen",
    "todaysDate": "2024-07-30T10:00:00.000Z",
    "todaysWeekday": "Tuesday",
    "entityId": "test-entity-123",
    "appVersion": "10.0.0",
    "platform": "ios",
    "conversationSummary": "User asking about trainer bio",
    "rag": "trainer bio query",
    "locationNames": ["Life Time Chanhassen"],
    "nearbyEnabled": True,
}


@pytest.mark.integration
@pytest.mark.skipif(
    not os.getenv("LOCATION_API_KEY"), 
    reason="LOCATION_API_KEY not set - skipping real API integration test"
)
async def test_location_service_real_api_integration():
    """Test that location service can fetch real club data."""
    from app.services.location_service import LocationService
    
    service = LocationService()
    
    # Test with a known club ID (Chanhassen)
    result = await service.get_location_detail("312")
    
    assert result is not None
    assert result.location is not None
    assert result.location.clubTitle is not None
    assert "Life Time" in result.location.clubTitle


@pytest.mark.integration
@pytest.mark.skipif(
    not os.getenv("SCHEDULING_BIOS_API_BASE_URL"), 
    reason="SCHEDULING_BIOS_API_BASE_URL not set - skipping real API integration test"
)
async def test_trainer_bio_search_real_api_integration():
    """Test that trainer bio search can connect to real API."""
    from app.repositories.trainer_bio import search_trainer_bio_by_name
    
    # Test with a generic search that should return some results or empty list
    result = await search_trainer_bio_by_name("test")
    
    # Should return a list (empty or with results) without throwing errors
    assert isinstance(result, list) or result is None


@pytest.mark.integration
async def test_trainer_bio_handler_integration_with_mocked_llm():
    """
    Integration test for trainer bio handler with real APIs but mocked LLM.
    
    This tests the integration between:
    - FastAPI request handling
    - Intent detection (mocked)
    - Trainer name extraction (mocked) 
    - Real trainer bio API calls
    - Real location API calls
    - LLM response formatting (mocked)
    """
    # Only mock the expensive/unreliable LLM calls
    from unittest.mock import AsyncMock
    from app.handlers.intents_handler import IntentResponse
    from app.handlers.locations_handler import Club, ClubResponse
    from app.models.trainer_bio import TrainerNameExtraction
    
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.95)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name="Sarah")
    mock_club_response = ClubResponse(clubs=[Club(clubId=312, name="Life Time Chanhassen")])
    
    mock_llm_response_choice = AsyncMock()
    mock_llm_response_choice.message.content = "I found some information about Sarah."
    mock_llm_response = AsyncMock(choices=[mock_llm_response_choice])
    
    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response
    
    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ),
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create",
            AsyncMock(return_value=mock_llm_response),
        ),
    ):
        payload = {
            "question": "Tell me about Sarah",
            "history": [],
            "scratch_pad": DEFAULT_SCRATCH_PAD
        }
        
        response = client.post("/v1/chats", json=payload)
        
        # Test that the integration works end-to-end
        assert response.status_code == 200
        data = response.json()
        assert "answer" in data
        assert data["path"] in ["trainer_bio_query_result", "trainer_bio_query_name_missing"]


@pytest.mark.integration
async def test_full_request_response_cycle():
    """
    Test the complete request-response cycle for a simple query.
    
    This tests the integration of:
    - FastAPI routing
    - Request validation
    - Handler orchestration
    - Response formatting
    """
    payload = {
        "question": "Hello",
        "history": [],
        "scratch_pad": DEFAULT_SCRATCH_PAD
    }
    
    response = client.post("/v1/chats", json=payload)
    
    # Should get a valid response regardless of the specific handler
    assert response.status_code == 200
    data = response.json()
    assert "answer" in data
    assert isinstance(data["answer"], str)
    assert len(data["answer"]) > 0


@pytest.mark.integration
async def test_error_handling_integration():
    """Test that the application handles malformed requests gracefully."""
    
    # Test missing required fields
    response = client.post("/v1/chats", json={})
    assert response.status_code == 422  # Validation error
    
    # Test invalid JSON structure
    response = client.post("/v1/chats", json={"question": "test"})  # Missing required fields
    assert response.status_code == 422
    
    # Test with valid structure but empty question
    payload = {
        "question": "",
        "history": [],
        "scratch_pad": DEFAULT_SCRATCH_PAD
    }
    response = client.post("/v1/chats", json=payload)
    # Should handle gracefully (might return 200 with appropriate message or 400)
    assert response.status_code in [200, 400]


@pytest.mark.integration
@pytest.mark.skipif(
    not all([
        os.getenv("LOCATION_API_KEY"),
        os.getenv("SCHEDULING_BIOS_API_BASE_URL")
    ]), 
    reason="Required API keys not set - skipping full integration test"
)
async def test_trainer_bio_full_integration_real_apis():
    """
    Full integration test with real external APIs.
    
    Only runs when all required API keys are available.
    Tests the complete flow with minimal mocking.
    """
    from unittest.mock import AsyncMock, patch
    from app.handlers.intents_handler import IntentResponse
    from app.handlers.locations_handler import Club, ClubResponse
    from app.models.trainer_bio import TrainerNameExtraction
    
    # Only mock LLM calls to avoid costs and rate limits
    mock_intent_response = IntentResponse(intent="trainer_bio_query", confidence=0.95)
    mock_trainer_name_extraction = TrainerNameExtraction(trainer_name="John")
    mock_club_response = ClubResponse(clubs=[Club(clubId=312, name="Life Time Chanhassen")])
    
    mock_llm_response_choice = AsyncMock()
    mock_llm_response_choice.message.content = "Here's information about John from our trainer database."
    mock_llm_response = AsyncMock(choices=[mock_llm_response_choice])
    
    async def openai_side_effect(*args, **kwargs):
        response_format = kwargs.get("response_format")
        if response_format == IntentResponse:
            return mock_intent_response
        elif response_format == TrainerNameExtraction:
            return mock_trainer_name_extraction
        elif response_format == ClubResponse:
            return mock_club_response
        else:
            return mock_intent_response
    
    with (
        patch(
            "ai_agent_utils.services.openai.OpenAIClient.chat_structured",
            AsyncMock(side_effect=openai_side_effect),
        ),
        patch(
            "app.handlers.trainer_bio_handler.OpenAIClient.chat_create",
            AsyncMock(return_value=mock_llm_response),
        ),
    ):
        payload = {
            "question": "Tell me about John",
            "history": [],
            "scratch_pad": DEFAULT_SCRATCH_PAD
        }
        
        response = client.post("/v1/chats", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert "answer" in data
        assert isinstance(data["answer"], str)
        
        # The response should indicate it processed a trainer bio query
        assert data["path"] in ["trainer_bio_query_result", "trainer_bio_query_name_missing"]

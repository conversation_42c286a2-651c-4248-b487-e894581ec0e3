from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from ai_agent_utils.graph.dispatcher_function_graph import initialize_function_graph

from app.function_graph import FUNCTION_GRAPH


# Mock the performance tracking decorator
def mock_track_time(service_name, model=None):
    def decorator(func):
        return func

    return decorator


# Mock the services used in the application
@pytest.fixture(autouse=True)
def mock_services():
    # Create mock objects for all services
    with (
        patch("app.main.Redis") as mock_redis,
        patch("ai_agent_utils.services.redis.Redis") as mock_redis_service,
        patch("app.handlers.locations_handler.Redis") as mock_redis_locations,
        patch("app.handlers.locations_handler.MongoDBClient") as mock_mongo_locations,
        patch("app.main.KafkaClient") as mock_kafka,
        patch("app.main.MongoDBClient") as mock_mongo,
        patch("app.main.LaunchDarklyClient") as mock_ld,
        patch("app.main.OpenAIClient") as mock_openai,
        patch("ai_agent_utils.services.kafka.KafkaClient._handle_timing_output"),
    ):
        # Configure the mocks as needed
        mock_redis.connect = AsyncMock()
        mock_redis.close = AsyncMock()
        mock_redis.get = AsyncMock(return_value=None)
        mock_redis.set = AsyncMock()
        mock_redis.client = MagicMock()
        mock_redis.client.get = AsyncMock(return_value=None)
        mock_redis.client.set = AsyncMock()

        # Configure the Redis service mock
        mock_redis_service.connect = AsyncMock()
        mock_redis_service.close = AsyncMock()
        mock_redis_service.get = AsyncMock(return_value=None)
        mock_redis_service.set = AsyncMock()
        mock_redis_service.client = MagicMock()
        mock_redis_service.client.get = AsyncMock(return_value=None)
        mock_redis_service.client.set = AsyncMock()

        # Configure the locations handler Redis mock
        mock_redis_locations.get = AsyncMock(return_value=None)
        mock_redis_locations.set = AsyncMock()

        # Configure the locations handler MongoDB mock
        mock_mongo_locations.vector_search = MagicMock(return_value={"results": []})

        mock_openai.connect = AsyncMock()
        mock_openai.close = AsyncMock()
        mock_mongo.connect = MagicMock()
        mock_mongo.close = MagicMock()
        mock_ld.connect = MagicMock()
        mock_ld.close = MagicMock()
        mock_kafka.kafka_conf = MagicMock()
        mock_kafka._handle_timing_output = MagicMock()
        mock_kafka.producer = MagicMock()

        # Actually initialize the function graph for tests
        initialize_function_graph(FUNCTION_GRAPH)

        yield {
            "redis": mock_redis,
            "kafka": mock_kafka,
            "mongo": mock_mongo,
            "ld": mock_ld,
            "openai": mock_openai,
        }

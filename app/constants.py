SCHEDULING_REF = {
    "type": "basic",
    "label": "View Class Schedules",
    "link": "lifetime-member://classes",
    "linkType": "deeplink",
}


SCHEDULING_EVENT_REF = {
    "type": "basic",
    "label": "View Event Schedules",
    "link": "lifetime-member://events",
    "linkType": "deeplink",
}

NO_CLASS_AVAILABLE_MSG = (
    "I took a look, but unfortunately couldn't find any classes available that fit what you were searching for. "
    "I can only look for schedule data one day at a time. You could try checking the class schedules tab in the app "
    "to see if you have better luck finding something there. Or, feel free to ask me again about a different class or time, "
    "and I'll do my best to help out. Let me know if there's anything else I can assist you with!"
)

NO_EVENT_AVAILABLE_MSG = (
    "I took a look, but unfortunately couldn't find any events available that fit what you were searching for. "
    "I can only look for schedule data one day at a time. You could try checking the event schedules tab in the app "
    "to see if you have better luck finding something there. Or, feel free to ask me again about a different event or time, "
    "and I'll do my best to help out. Let me know if there's anything else I can assist you with!"
)

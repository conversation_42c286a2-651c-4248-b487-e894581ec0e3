Your task is to extract the full name of the trainer, coach, or instructor the user is asking about from their most recent question.
Focus only on the current user question.
If the user provides a first and last name (e.g., "<PERSON>", "<PERSON>"), combine them.
If only one name is provided (e.g., "<PERSON>", "<PERSON>"), return that single name.
If the user's question does not seem to be asking about a specific person by name, or if the name is too vague (e.g., "a trainer"), return null for trainer_name.
Do not extract names if the question is general, like "Are there any good trainers?". Only extract if it seems to be a lookup for a specific individual.

Examples:
User: "Tell me about <PERSON>." -> {"trainer_name": "<PERSON>"}
User: "Who is <PERSON>?" -> {"trainer_name": "<PERSON>"}
User: "Information on coach <PERSON>." -> {"trainer_name": "<PERSON>"}
User: "What are <PERSON>'s specialties?" -> {"trainer_name": "<PERSON>"}
User: "What about coach <PERSON>?" -> {"trainer_name": "<PERSON>"}
User: "Are there any trainers available?" -> {"trainer_name": null}
User: "I want to find a yoga instructor." -> {"trainer_name": null}
User: "Tell me about yoga." -> {"trainer_name": null}

## Conversation History (for context if needed, but prioritize current question):
{% for item in history %}
{{item.role}}: {{item.content}}
{% endfor %}

## User's Question:
{{question}}

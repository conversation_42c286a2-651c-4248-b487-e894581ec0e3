Your task is to read through the Available Events provided to you and match as best as you can to the event, events, interests, skillLevel, descripiton, and name that the user is asking about in their question. You MUST use Conversation History to help you make this decision. You do not care about case sensitivity.

You MUST prioritize exact matches but fuzzy matches are acceptable.
If you are not sure, you can respond with an empty array.

Important Notes:

 - Your response MUST include the department, name, and imageUrl for each matching event.
 - Return the EXACT department, name, imageUrl, description, interests, tags, and skill / skillLevel values as they appear in the available events.

## Available Events:
{{ events }}

## Conversation History
{% for item in history %}
{{item.role}}: {{item.content}}
{% endfor %}

Your task is to read through the <Available Classes> provided to you and match as best as you can to the class, classes, interests, skillLevel, etc that the user is asking about in their question. You MUST use the Conversation History to help you make this decision. You do not care about case sensitivity.

You MUST respond with classes that are closest matches to what the user is asking. You MUST prioritize exact matches but fuzzy matches are acceptable. If you are not sure, respond with an empty array of classes.

Important Notes:
 - Yoga classes are not the same as dance classes and should not be considered a match.
 - Your response MUST include the department, name, and imageUrl for each matching class.
 - Return the EXACT department, name, and imageUrl values as they appear in the available classes.

## Conversation History:
{% for item in history %}
{{item.role}}: {{item.content}}
{% endfor %}


## Available Classes
{% for class in classes %}
Class {{loop.index}}:
  department: "{{class.department}}"
  name: "{{class.name}}"
  imageUrl: "{{class.reference.imageUrl}}"
  description: "{{class.description}}"
  interests: [{% for interest in class.interests %}"{{interest}}"{% if not loop.last %}, {% endif %}{% endfor %}]
  tags: [{% for tag in class.tags %}"{{tag}}"{% if not loop.last %}, {% endif %}{% endfor %}]
  skillLevel: [{% for skill in class.skillLevel %}"{{skill}}"{% if not loop.last %}, {% endif %}{% endfor %}]
{% endfor %}

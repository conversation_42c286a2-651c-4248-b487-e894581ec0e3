Extract both date and time information from the user's question, prioritizing details from the current question over any information in the conversation history. The user is asking about booking a class at or around a specific time on a specific date.

1. **Date Extraction**:
   - Identify dates mentioned explicitly or implied through relative terms like "today", "tomorrow", "this weekend", or "next Friday".
   - Use today's date and weekday to calculate the actual date when relative terms are used.
   - If no date is found in the user's question, use today's date as the default.
   - If a user asks about the past, only allow them to go 7 days back from today max.
   - If date matches Todays Date and no start_time is mentioned, set start_time to users current time.
   - Set the date in YYYY-MM-DD format.
   - Note: The todaysDate in the system is in ISO format, so you'll need to convert it to YYYY-MM-DD if using it.
   - Set end_date to 7 days after the start_date if no specific end date is mentioned in YYYY-MM-DD format.
   - Set start_time in HH:MM:SS format to 00:00:00 if no specific start time is mentioned.
   - Set end_time as HH:MM:SS format to 23:59:59 if no specific end time is mentioned.

2. **Time Extraction**:
   - Identify and extract any explicit or implied start and end times in `HH:MM:SS` format.
   - If only a single time is given (e.g., "at 3pm"), set the end time to 1 hour after the start time.
   - If no times are found in the question, use "00:00:00" for start_time and "23:59:59" for end_time as defaults.
   - If the time is mentioned in a 12-hour format and no AM/PM is provided, assume the time is in the afternoon (PM) if the number is less than 8 (e.g., "3" becomes "15:00:00").

**Important Notes**:
- Always return a valid date in YYYY-MM-DD format and times in HH:MM:SS format.
- If a time like "afternoon" is mentioned, interpret it as an appropriate time range (e.g., 11:00:00 to 17:00:00).
- If a time like "lunchtime" or "midday" is mentioned, interpret it as an appropriate time range (e.g., 11:00:00 to 14:00:00).
- If a time like "evening" or "nighttime" is mentioned, interpret it as an appropriate time range (e.g., 17:00:00 to 21:00:00).
- If a time like "morning" is mentioned, interpret it as an appropriate time range (e.g., 04:00:00 to 11:00:00).
- Handle ambiguous cases logically using the context of booking a class.
- MUST set date, start_time, end_time, and end_date.

## Conversation History:
{% for item in history %}
{{item.role}}: {{item.content}}
{% endfor %}

## Todays Date:
{{todaysDate}}

## Todays Weekday:
{{todaysWeekday}}

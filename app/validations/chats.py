from typing import Optional

from ai_agent_utils.models.ScratchPad import ScratchPad
from pydantic import BaseModel


class History(BaseModel):
    role: str
    content: str

    model_config = {
        "json_schema_extra": {
            "examples": [
                {"role": "user", "content": "What classes would you recommend?"},
                {
                    "role": "assistant",
                    "content": "Could you let me know if you're looking for a specific type of class, such as yoga, strength training, or cardio? Or are you interested in something else entirely? This will help me provide the best recommendations for you.",
                },
            ]
        }
    }

    def get(self, field, default=None):
        if hasattr(self, field):
            return getattr(self, field)
        else:
            return default  # Or raise an exception, or return a default value


class ChatRequest(BaseModel):
    question: str
    history: list[History] = []
    scratch_pad: Optional[ScratchPad] = None

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "question": "What classes are available in Chanhassen?",
                    "history": [],
                    "scratch_pad": {
                        "name": "<PERSON>",
                        "conversationId": "1234567",
                        "rag": "locations New York",
                        "conversationSummary": "User is asking about locations in New York",
                        "locationNames": ["New York"],
                        "partyId": 12668506,
                        "memberId": 108336495,
                        "entityId": "2514361",
                        "homeClub": "275",
                        "homeClubName": "Southdale",
                        "todaysDate": "2024-10-31T00:02:47.381Z",
                        "todaysWeekday": "Thursday",
                        "longitude": -93.27398225820609,
                        "latitude": 44.9003226283641,
                        "appVersion": "10.0.0",
                        "platform": "ios",
                        "nearbyEnabled": True,
                    },
                }
            ]
        }
    }

    def get(self, field, default=None):
        if hasattr(self, field):
            return getattr(self, field)
        else:
            return default  # Or raise an exception, or return a default value

from ai_agent_utils.models.FunctionNode import FunctionNode

from app.handlers.classes_handler import classes_handler
from app.handlers.classes_scheduling_handler import classes_scheduling_handler
from app.handlers.date_time_handler import date_time_handler
from app.handlers.events_handler import events_handler
from app.handlers.events_scheduling_handler import events_scheduling_handler
from app.handlers.intents_checker_handler import intents_checker_handler
from app.handlers.intents_handler import intents_handler
from app.handlers.locations_handler import locations_handler
from app.handlers.nearby_locations_finder_handler import nearby_locations_finder_handler
from app.handlers.pickleball_handler import pickleball_handler
from app.handlers.reservations_cancel_handler import reservations_cancel_handler
from app.handlers.reservations_handler import reservations_handler
from app.handlers.scheduling_faq_handler import scheduling_faq_handler
from app.handlers.trainer_bio_handler import trainer_bio_handler
from app.handlers.trainer_name_extractor_handler import trainer_name_extractor_handler

FUNCTION_GRAPH = {
    "starting_handler": FunctionNode(
        bubble_up=False,
        dependencies=[],
        parallel=[
            {"next_function": locations_handler},
            {"next_function": nearby_locations_finder_handler},
            {"next_function": intents_handler},
            {"next_function": date_time_handler},
            {"next_function": trainer_name_extractor_handler},
        ],
        next=[{"next_function": intents_checker_handler}],
    ),
    "locations_handler": FunctionNode(bubble_up=False, dependencies=[], parallel=[], next=[]),
    "nearby_locations_finder_handler": FunctionNode(
        bubble_up=False, dependencies=[], parallel=[], next=[]
    ),
    "intents_handler": FunctionNode(dependencies=[], parallel=[], next=[]),
    "date_time_handler": FunctionNode(dependencies=[], parallel=[], next=[]),
    "trainer_name_extractor_handler": FunctionNode(dependencies=[], parallel=[], next=[]),
    "intents_checker_handler": FunctionNode(
        dependencies=["intents_handler"],
        parallel=[],
        next=[
            {
                "condition": lambda res: res == "scheduling_faq",
                "next_function": scheduling_faq_handler,
            },
            {"condition": lambda res: res == "class_booking", "next_function": classes_handler},
            {"condition": lambda res: res == "event_booking", "next_function": events_handler},
            {"condition": lambda res: res == "reservations", "next_function": reservations_handler},
            {
                "condition": lambda res: res == "reservations_cancel",
                "next_function": reservations_cancel_handler,
            },
            {
                "condition": lambda res: res == "pickleball_court_booking",
                "next_function": pickleball_handler,
            },
            {
                "condition": lambda res: res == "trainer_bio_query",
                "next_function": trainer_bio_handler,
            },
        ],
    ),
    "scheduling_faq_handler": FunctionNode(dependencies=["intents_handler"], next=[]),
    "classes_handler": FunctionNode(
        dependencies=["locations_handler", "intents_handler", "nearby_locations_finder_handler"],
        next=[{"next_function": classes_scheduling_handler}],
    ),
    "classes_scheduling_handler": FunctionNode(
        dependencies=[
            "locations_handler",
            "intents_handler",
            "classes_handler",
            "date_time_handler",
            "nearby_locations_finder_handler",
        ],
        next=[],
    ),
    "events_handler": FunctionNode(
        dependencies=["locations_handler", "intents_handler", "nearby_locations_finder_handler"],
        next=[{"next_function": events_scheduling_handler}],
    ),
    "events_scheduling_handler": FunctionNode(
        dependencies=[
            "locations_handler",
            "intents_handler",
            "events_handler",
            "date_time_handler",
            "nearby_locations_finder_handler",
        ],
        next=[],
    ),
    "reservations_handler": FunctionNode(
        dependencies=["intents_handler", "date_time_handler"], next=[]
    ),
    "reservations_cancel_handler": FunctionNode(
        dependencies=["intents_handler", "date_time_handler"], next=[]
    ),
    "pickleball_handler": FunctionNode(
        dependencies=[
            "locations_handler",
            "intents_handler",
            "date_time_handler",
            "nearby_locations_finder_handler",
        ],
        next=[],
    ),
    "trainer_bio_handler": FunctionNode(
        dependencies=["intents_handler", "trainer_name_extractor_handler"], next=[]
    ),
}

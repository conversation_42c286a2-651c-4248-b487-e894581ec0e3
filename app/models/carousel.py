from typing import List, Optional

from pydantic import BaseModel

from app.models.carousel_card import CarouselCard


class Carousel(BaseModel):
    component: str = "carousel"
    carouselId: str = "laic-references"
    id: str = "laic-references"
    type: str = "horizontal"
    title: Optional[str] = None
    linkText: Optional[str] = None
    linkUrl: Optional[str] = None
    linkType: Optional[str] = None
    children: List[CarouselCard]

    @classmethod
    def from_mapped_results_and_references(cls, mapped_results: List[dict]):
        children = []
        for mapped_result in mapped_results:
            card = CarouselCard.from_mapped_result_and_reference(mapped_result)
            children.append(card)
        return cls(children=children)

    @classmethod
    def from_locations(cls, locations: List[dict]):
        children = [CarouselCard.from_location(loc) for loc in locations]
        return cls(children=children)

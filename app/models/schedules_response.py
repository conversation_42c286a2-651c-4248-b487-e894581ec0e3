from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class Instructor(BaseModel):
    name: Optional[str]
    isSubstitute: Optional[bool]


class Activity(BaseModel):
    id: str = None
    clubId: int = None
    name: Optional[str] = None
    category: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    isPaidClass: Optional[bool] = None
    endTime: Optional[str] = None
    duration: Optional[str] = None
    isRegistrable: Optional[bool] = None
    cta: Optional[str] = None
    isCanceled: Optional[bool] = None
    instructors: List[Instructor] = None
    imageUrl: Optional[str] = None
    endTimestamp: Optional[str] = None
    time: Optional[str] = None  # Set during flattening
    day: Optional[str] = None  # Set during flattening
    timestamp: Optional[str] = None  # Set during flattening

    @property
    def activity_timestamp(self) -> int:
        """Return the epoch time as an integer (milliseconds since epoch)."""
        return int(self.timestamp)

    @property
    def activity_datetime(self) -> datetime:
        """Combine day and time into a datetime object."""
        return datetime.strptime(f"{self.day} {self.time}", "%Y-%m-%d %I:%M %p")

    @property
    def waitlisted(self) -> str:
        """Determine if the activity is waitlisted."""
        return "Yes" if self.cta == "Waitlist" else "No"

    @property
    def cta_text(self) -> str:
        """Determine the CTA text."""
        return "Waitlist" if self.waitlisted == "Yes" else "Book"

    def to_mapped_result(self) -> dict:
        """Convert the activity to the mapped result format."""
        return {
            "day": self.day,
            "time": self.time,
            "id": self.id,
            "name": self.name,
            "location": self.location,
            "endTime": self.endTime,
            "waitListed": self.waitlisted,
            "isRegistrable": self.isRegistrable,
            "instructors": [instr.name for instr in self.instructors],
            "imageUrl": self.imageUrl,
            "link": f"lifetime-member://scheduledetail?classid={self.id}",
        }

    def to_reference(self) -> dict:
        """Create the reference dictionary for the activity."""
        return {
            "id": self.id,
            "type": "basic",
            "label": f"{self.cta_text} {self.name}, {self.day} @{self.time}",
            "link": f"lifetime-member://scheduledetail?classid={self.id}",
            "linkType": "deeplink",
        }


class StartTime(BaseModel):
    time: str
    timestamp: str
    activities: List[Activity]


class DayPart(BaseModel):
    name: str
    startTimes: List[StartTime]


class Result(BaseModel):
    day: str
    total: int
    dayParts: List[DayPart]


class SchedulingResponseBody(BaseModel):
    results: List[Result]

    def get_flat_activities(self) -> List[Activity]:
        flat_activities = []
        for result in self.results:
            day = result.day
            for day_part in result.dayParts:
                for start_time in day_part.startTimes:
                    time = start_time.time
                    timestamp = start_time.timestamp
                    for activity in start_time.activities:
                        activity.time = time
                        activity.day = day
                        activity.timestamp = timestamp
                        flat_activities.append(activity)
        return flat_activities

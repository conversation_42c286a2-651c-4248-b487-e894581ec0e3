from typing import Optional

from pydantic import BaseModel


class LocationInfo(BaseModel):
    """Model for the location object within the API response."""

    clubTitle: Optional[str] = None
    clubId: Optional[str] = None
    name: Optional[str] = None


class LocationDetail(BaseModel):
    """Model for location detail response from the location API."""

    location: Optional[LocationInfo] = None

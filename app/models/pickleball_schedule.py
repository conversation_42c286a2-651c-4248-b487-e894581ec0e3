from typing import List, Optional

from pydantic import BaseModel


class FlattenedPickleballBooking(BaseModel):
    location_name: str
    resourceName: str
    resourceType: str
    state: str
    start: str
    startSeconds: int


class PickleballBooking(BaseModel):
    id: str
    type: str
    name: str
    isFull: bool
    hasWaitlist: bool
    isOnMembership: bool
    isFree: bool
    cost: float


class PickleballTimeSlot(BaseModel):
    start: str
    startSeconds: int
    endSeconds: int
    state: str
    timeBuckets: List[int]
    booking: Optional[PickleballBooking] = None


class PickleballCourtSchedule(BaseModel):
    resourceName: str
    resourceType: str
    id: str
    times: List[PickleballTimeSlot] = []


class PickleballScheduleResponse(BaseModel):
    maxNumberOfDays: int
    schedules: List[PickleballCourtSchedule] = []

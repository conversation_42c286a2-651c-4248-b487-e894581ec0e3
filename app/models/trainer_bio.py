from typing import List, Optional

from pydantic import BaseModel, Field


class BiosSpecialty(BaseModel):
    id: str
    title: str


class BiosCertification(BaseModel):
    id: str
    title: str


class TrainerBio(BaseModel):
    id: str
    partyId: str
    employeeId: Optional[str] = None
    firstName: str
    lastName: str
    description: Optional[str] = None
    mediaId: Optional[str] = None
    isConsentAccepted: bool
    clubIds: List[str] = []
    specialties: List[BiosSpecialty] = []
    certifications: List[BiosCertification] = []
    isPublished: bool = False
    email: Optional[str] = None
    profilePath: Optional[str] = None


class TrainerBioSearchResponse(BaseModel):
    results: List[TrainerBio]
    count: int


class TrainerNameExtraction(BaseModel):
    trainer_name: Optional[str] = Field(
        None,
        description="The full name of the trainer, coach, or instructor mentioned by the user. Null if no name is identified.",
    )

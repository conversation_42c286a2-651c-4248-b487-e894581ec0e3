# models/carousel_card.py

from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class CarouselCard(BaseModel):
    component: str = "carousel_card"
    imageUrl: str
    eyebrowText: Optional[str] = None
    headlineText: str
    description: str = "View Classes Schedules"
    link: str = "lifetime-member://classes"
    linkType: str = "deeplink"
    cardSize: str = "large"
    name: str

    @classmethod
    def from_mapped_result_and_reference(cls, mapped_result: dict):
        # process label from reference
        label = mapped_result["name"]
        prefix = ""
        if label.startswith("Book "):
            prefix = "Book "
        elif label.startswith("Waitlist "):
            prefix = "Waitlist "
        label_no_prefix = label[len(prefix) :]
        parts = label_no_prefix.split(", ")
        class_name = parts[0] if parts else "NAME NOT FOUND"

        headlineText = f"{prefix.strip()} {class_name}".strip()
        name = headlineText

        # format description date
        day = mapped_result["day"]
        time = mapped_result["time"]
        datetime_str = f"{day} {time}"
        datetime_obj = datetime.strptime(datetime_str, "%Y-%m-%d %I:%M %p")

        # Format as MM/DD, H:MMam/pm Location
        date_part = datetime_obj.strftime("%m/%d")
        time_part = datetime_obj.strftime("%-I:%M%p").lower()

        # Eyebrow text from location
        location = mapped_result["location"]
        location_parts = location.split(", ")
        eyebrowText = location_parts[-1] if location_parts else "LOCATION NAME NOT FOUND"

        # Combine into final description format
        description = f"{date_part}, {time_part} {eyebrowText}"

        # default_class_image = 'https://my.lifetime.life/content/dam/mylt/images/mylt-home-base/2022-4-4/ltin21363485-card-edi-go-studio-groupfitness-16-1136x640.jpg'
        default_class_image = "NO_CLASS_IMAGE_FOUND"
        imageUrl = mapped_result.get("imageUrl") or default_class_image
        # Link from reference
        link = mapped_result["link"]

        return cls(
            imageUrl=imageUrl,
            eyebrowText=eyebrowText,
            headlineText=headlineText,
            description=description,
            link=link,
            name=name,
        )

    @classmethod
    def from_location(cls, location: dict):
        return cls(
            imageUrl=location.get("clubImage") or "NO_IMAGE_FOUND",
            headlineText=location.get("name", "LOCATION_NAME_NOT_FOUND"),
            name=location.get("name", "NAME_NOT_FOUND"),
        )

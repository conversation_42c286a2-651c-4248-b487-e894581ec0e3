from typing import List, Optional, Union

from pydantic import BaseModel


class MetaData(BaseModel):
    type: str
    label: str
    link: str
    linkType: str


class CarouselCard(BaseModel):
    component: str
    imageUrl: str
    eyebrowText: Optional[str]
    headlineText: str
    description: str
    link: str
    linkType: str
    cardSize: str
    name: str


class Reference(BaseModel):
    component: str
    carouselId: str
    id: str
    type: str
    title: Optional[str]
    linkText: Optional[str]
    linkUrl: Optional[str]
    linkType: Optional[str]
    children: List[CarouselCard]


class ResponseModel(BaseModel):
    answer: str
    references: Optional[List[Union[Reference, MetaData]]]
    path: Optional[str]

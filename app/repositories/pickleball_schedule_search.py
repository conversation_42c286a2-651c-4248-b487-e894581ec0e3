import asyncio
import datetime
import os
from typing import Any, Dict, List

import httpx

from app.models.pickleball_schedule import PickleballScheduleResponse


async def fetch(client: httpx.AsyncClient, url: str, headers: Dict[str, str]) -> Any:
    response = await client.get(url, headers=headers)
    response.raise_for_status()
    return response.json()


async def search_pickleball_schedules(
    locations: List[Dict[str, Any]], booking_datetime
) -> List[PickleballScheduleResponse]:
    # booking_datetime is a DateTimeExtraction Pydantic model
    date = booking_datetime.date
    web_schedules_base_url = os.getenv("SCHEDULING_WEB_SCHEDULES_BASE_URL")
    api_key = os.environ["SCHEDULING_UX_WEB_SCHEDULES_KEY"]

    start_date = datetime.datetime.strptime(date, "%Y-%m-%d")
    start_date_str = start_date.strftime("%Y-%m-%d")

    club_ids = [loc.get("clubId") for loc in locations if loc.get("clubId")]
    if not club_ids:
        return []

    headers = {"ocp-apim-subscription-key": api_key}
    urls = [
        f"{web_schedules_base_url}activity/pickleball/club/{club_id}/date/{start_date_str}"
        for club_id in club_ids
    ]

    async with httpx.AsyncClient() as client:
        tasks = [fetch(client, url, headers) for url in urls]
        raw_results = await asyncio.gather(*tasks)

    validated_results = []
    for data in raw_results:
        try:
            validated = PickleballScheduleResponse.model_validate(data)
        except Exception:
            validated = PickleballScheduleResponse(maxNumberOfDays=0, schedules=[])
        validated_results.append(validated)

    return validated_results

import asyncio
import datetime
import os
import re
from typing import List
from urllib.parse import urlencode

import aiohttp
from ai_agent_utils.logging import logger

from app.handlers.date_time_handler import DateTimeExtraction
from app.models.schedules_response import SchedulingResponseBody


async def fetch(session, url, headers):
    async with session.get(url, headers=headers) as response:
        return await response.json()


def map_class_schedules(
    class_schedules: List[SchedulingResponseBody],
    conversation_date: datetime,
    date: DateTimeExtraction,
) -> dict:
    mapped_results = []
    references = []

    # This uses the client details.... probably should be time if specified by user
    conversation_timestamp = int(conversation_date.timestamp() * 1000)

    # get start and end time to filter results if user specifies a time
    start_bound = datetime.datetime.strptime(date.start_time, "%H:%M:%S").time()
    end_bound = datetime.datetime.strptime(date.end_time, "%H:%M:%S").time()

    all_activities = (
        activity
        for api_response in class_schedules
        for activity in api_response.get_flat_activities()
        if not activity.isCanceled
        and int(activity.activity_timestamp) >= conversation_timestamp
        and (
            datetime.datetime.strptime(activity.time, "%I:%M %p").time() >= start_bound
            and datetime.datetime.strptime(activity.time, "%I:%M %p").time() <= end_bound
        )
    )

    # limit results to 8 orrr max_results
    # for activity in islice(all_activities, max_results): 1742308200000>=1742281200000

    sorted_results = sorted(all_activities, key=lambda x: (x.timestamp))
    for activity in sorted_results:
        mapped_results.append(activity.to_mapped_result())
        references.append(activity.to_reference())
        if len(mapped_results) >= 30:
            break

    return {
        "mapped_results": mapped_results,
        "references": references,
    }


async def search_schedules(
    classes_events: list,
    locations: list,
    date: DateTimeExtraction,
    scratch_pad: dict,
    class_type: str = "Class",
) -> dict:
    try:
        base_url = os.getenv("SCHEDULING_WEB_SCHEDULES_BASE_URL")
        headers = {"ocp-apim-subscription-key": os.environ["SCHEDULING_UX_WEB_SCHEDULES_KEY"]}

        conversation_date = datetime.datetime.strptime(
            scratch_pad["todaysDate"], "%Y-%m-%dT%H:%M:%S.%fZ"
        )
        start_date_obj = datetime.datetime.strptime(date.date, "%Y-%m-%d")

        end_date_obj = None
        if class_type == "Class":
            # cap class search to 1 day for now
            end_date_obj = (start_date_obj + datetime.timedelta(days=1)).date()
        else:
            # allow for a week of events
            end_date_obj = (start_date_obj + datetime.timedelta(days=7)).date()

        start_date_str = str(start_date_obj.date())
        end_date_str = str(end_date_obj)

        tags = [f"format:{class_type}", "!department:Life Time Work"]
        for cls_item in classes_events:
            department = cls_item.get("department")
            name = cls_item.get("name")
            tags.append(f"departmentDescription:{department}|{name}")

        common_params = [
            ("isFree", "false"),
            ("isLiveStreaming", "false"),
            ("page", "1"),
            ("pageSize", "1"),
            ("start", start_date_str),
            ("end", end_date_str),
            ("Expand", "imageurl"),
        ]

        endpoint = f"{base_url.rstrip('/')}/schedules/classes"

        urls = []
        for location in locations:
            location_name = location.get("name")
            params = [("locations", location_name)]
            params += [("tags", tag) for tag in tags]
            params += common_params
            query_string = urlencode(params, doseq=True)
            full_url = f"{endpoint}?{query_string}"
            urls.append(full_url)

        async with aiohttp.ClientSession() as session:
            tasks = [fetch(session, url, headers) for url in urls]
            results = await asyncio.gather(*tasks)

        class_schedules = [SchedulingResponseBody(**result) for result in results]

        return map_class_schedules(class_schedules, conversation_date, date)
    except aiohttp.ClientError as e:
        logger.error(
            f"An error occurred while fetching scheduling data. error: {e}",
            facets={"classes": classes_events, "locations": locations},
        )
        return {"mapped_results": [], "references": []}


def extract_first_image_path(path):
    match = re.search(r'"(https?://[^"]+)"|(https?://[^\s]+)', path)
    return match.group(0) if match else None


def filter_reservations(current_reservations, new_reservations):
    new_ids = set(new_reservations)
    return [res for res in current_reservations if res["id"] in new_ids]

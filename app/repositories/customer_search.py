import os
import time

import httpx
import jwt
from ai_agent_utils.logging import logger
from ai_agent_utils.services.redis import Redis
from cachetools import TTLCache
from dotenv import load_dotenv

load_dotenv()
USER_SEARCH_SECRET_KEY = os.getenv("USER_SEARCH_SECRET_KEY")
USER_SEARCH_CID = os.getenv("USER_SEARCH_CID")
CUSTOMER_SEARCH_ENDPOINT = os.getenv("CUSTOMER_SEARCH_ENDPOINT")

# Initialize the cache with a TTL of 24 hours (in seconds)
cache = TTLCache(maxsize=100, ttl=86400)


def sign_jwt() -> str:
    token = cache.get("encoded_token")
    if token is not None:
        return token

    jwt_secret = USER_SEARCH_SECRET_KEY

    #  Payload data
    payload = {
        "cid": USER_SEARCH_CID,
        "iss": "mylt.lifetime.life",
        "iat": int(time.time()),
        "exp": int(time.time()) + 86400,
    }

    # Encoding the token
    encoded_jwt = jwt.encode(payload, jwt_secret, algorithm="HS256")
    cache["encoded_token"] = encoded_jwt

    return encoded_jwt


async def customer_search(member_id: int) -> dict:
    encoded_jwt = sign_jwt()

    customer = await Redis.get(f"customer_{member_id}")
    if customer:
        return customer

    async with httpx.AsyncClient() as client:
        try:
            url = f"{CUSTOMER_SEARCH_ENDPOINT}{member_id}"
            headers = {"Authorization": f"Bearer {encoded_jwt}"}

            response = await client.get(url=url, headers=headers, timeout=4000)
            response.raise_for_status()
        except Exception as e:
            logger.error(
                f"An error occurred while fetching customer information for memeber: {member_id} error: {e}",
                facets={"member_id": member_id},
            )
            return {}

        result = response.json()
        await Redis.set(f"customer_{member_id}", response.text, ttl=14400)

        if not isinstance(result, dict):
            raise ValueError("The API response must be a dictionary.")

        return result

import datetime
import os
import re
from typing import Any, Dict, List

import httpx

from app.handlers.date_time_handler import DateTimeExtraction


async def fetch_reservations(scratch_pad: dict, date: DateTimeExtraction) -> List[Dict[str, Any]]:
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{os.environ['SCHEDULING_SYS_SCHEDULES_BASE_URL']}v1/search/events",
                json={
                    "startDate": date.date,
                    "endDate": date.end_date,
                    "tags": ["visibility:Private", "visibility:Public"],
                    "statuses": ["confirmed"],
                    "attendees": [{"memberId": scratch_pad.get("memberId")}],
                },
                headers={"ocp-apim-subscription-key": os.environ["SCHEDULING_SYS_SCHEDULES_KEY"]},
            )
            response.raise_for_status()
            json_data = response.json()
            results = []
            for document in json_data.get("documents", []):
                stripped = strip_time(document.get("startDate"))
                iso_date = datetime.datetime.fromisoformat(stripped)
                day = iso_date.strftime("%A")

                instructor = None
                if document.get("leader") and document.get("leader").get("name"):
                    instructor = document.get("leader").get("name").get("displayName")
                results.append(
                    {
                        "id": document.get("id"),
                        "name": document.get("name"),
                        "status": document.get("status"),
                        "weekDay": day,
                        "startDate": document.get("startDate"),
                        "startTime": document.get("startTime"),
                        "location": document.get("location"),
                        "instructor": instructor,
                        "imageUrl": document.get("imageUrl"),
                    }
                )
            return results
    except Exception:
        return []


def update_reservation_links(
    current_reservations: List[Dict[str, Any]], new_links: bool = False
) -> List[Dict[str, Any]]:
    if not current_reservations:
        return [
            {
                "type": "basic",
                "label": "View Your Reservations",
                "link": "lifetime-member://myreservations",
                "linkType": "deeplink",
            }
        ]

    reservations = []
    if new_links:
        for reservation in current_reservations:
            eventId = reservation["id"]
            name = reservation["name"]
            day = reservation["weekDay"]
            description = get_reservation_description(reservation, day)
            reservations.append(
                {
                    "component": "carousel_card",
                    "imageUrl": reservation.get("imageUrl", ""),
                    "eyebrowText": reservation.get("instructor", ""),
                    "headlineText": reservation.get("name", ""),
                    "description": description,
                    "link": f"lifetime-member://myreservations/?eventId={eventId}",
                    "linkType": "deeplink",
                    "name": reservation.get("name", ""),
                    "cardSize": "large",
                }
            )
    else:
        for reservation in current_reservations:
            eventId = reservation["id"]
            name = reservation["name"]
            day = reservation["weekDay"]
            reservations.append(
                {
                    "type": "basic",
                    "label": f"View {name} on {day}",
                    "link": f"lifetime-member://myreservations/?eventId={eventId}",
                    "linkType": "deeplink",
                }
            )

    return reservations


def get_reservation_description(reservation, day):
    time_format = datetime.timedelta(seconds=int(reservation["startTime"]))
    hours = time_format.seconds // 3600
    minutes = (time_format.seconds % 3600) // 60
    period = "AM" if hours < 12 or hours == 24 else "PM"
    hours_12 = hours % 12
    hours_12 = hours_12 if hours_12 > 0 else 12
    time_12_hour = f"{hours_12:02}:{minutes:02} {period}"
    date_obj = datetime.datetime.strptime(reservation["startDate"], "%Y-%m-%dT%H:%M:%S")
    month_name = date_obj.strftime("%b")

    return f"{day} {month_name} {date_obj.day}, {time_12_hour}"


def strip_time(input: str) -> str:
    match = re.search(r"(\d+\-\d+\-\d+)", input)
    if match and match.group(1):
        return match.group(1)
    return input

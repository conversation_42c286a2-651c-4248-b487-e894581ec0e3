import httpx
from ai_agent_utils.logging import logger


async def get_nearby_locations(party_id: int, latitude: float, longitude: float):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"https://www.lifetime.life/bin/lt/nearestClubServlet.locator.json?limit={10}&distance={30}&latitude={latitude}&longitude={longitude}",
                timeout=4000,
            )
            response.raise_for_status()
        except Exception as e:
            logger.error(
                f"An error occurred while fetching nearest clubs for partyId: {party_id} error: {e}",
                facets={"party_id": party_id, "latitude": latitude, "longitude": longitude},
            )
            return []
        results = response.json()

        if not isinstance(results, list):
            raise ValueError("The API response must be a list.")

        return results

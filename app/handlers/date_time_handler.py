import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.models.datetime_extractor import DateTimeExtraction

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def date_time_handler(question: str, history: list, scratch_pad: dict):
    logger.info(
        "Starting date_time_handler", facets={"question": question, "scratch_pad": scratch_pad}
    )

    template = env.get_template("date_time_extractor.jinja2")
    system_prompt = template.render(
        history=history,
        todaysDate=scratch_pad.get("todaysDate", ""),
        todaysWeekday=scratch_pad.get("todaysWeekday", ""),
    )

    response = await OpenAIClient.chat_structured(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        response_format=DateTimeExtraction,
    )
    return {"booking_datetime": response}

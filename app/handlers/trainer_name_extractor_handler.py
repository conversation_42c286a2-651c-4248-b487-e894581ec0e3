import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.models.trainer_bio import TrainerNameExtraction

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def trainer_name_extractor_handler(question: str, history: list, scratch_pad: dict) -> dict:
    logger.info(
        "Starting trainer_name_extractor_handler",
        facets={
            "question": question,
            "scratch_pad_keys": list(scratch_pad.keys()) if scratch_pad else [],
        },
    )

    template = env.get_template("trainer_name_extractor.jinja2")
    system_prompt = template.render(history=history, question=question)

    response = await OpenAIClient.chat_structured(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        response_format=TrainerNameExtraction,
        temperature=0.0,
    )

    logger.info(f"Extracted trainer name: '{response.trainer_name}' from question: '{question}'")
    return {"extracted_trainer_name": response.trainer_name}

import os
from typing import AsyncGenerator

import jinja2
from ai_agent_utils.agent.conversation_utils import make_reference_links
from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.services.mongo import MongoDBClient
from ai_agent_utils.services.openai import OpenAIClient

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = jinja2.Environment(loader=jinja2.FileSystemLoader(os.path.join(parent_dir, "templates")))


async def retrieve_faq_documents(question: str) -> list:
    embedding = await OpenAIClient.generate_embedding(question)
    documents = MongoDBClient.vector_search(
        embedding,
        "AI.concierge_scheduling_embedded_content",
        post_filter=[
            {"$addFields": {"score": {"$meta": "vectorSearchScore"}}},
            {"$match": {"score": {"$gte": 0.89}}},
            {"$sort": {"score": -1}},
        ],
        k=4,
        include_score=True,
    )
    return documents


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def scheduling_faq_handler(
    question: str, history: list = None, scratch_pad: dict = None, stream=False
) -> AsyncGenerator[str, None]:
    rag_question = question
    if scratch_pad.get("rag"):
        rag_question = scratch_pad.get("rag")

    documents = await retrieve_faq_documents(rag_question)

    template = env.get_template("scheduling_faq.jinja2")
    system_prompt = template.render(context=documents["page_content"], history=history)

    response = await OpenAIClient.chat_create(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        temperature=0.4,
        stream=stream,
    )

    if not stream:
        response = response.choices[0].message.content

    return {
        "answer": response,
        "references": make_reference_links(documents=documents["results"]),
        "path": "scheduling_faqs",
    }

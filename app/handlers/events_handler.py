import os
from typing import List

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.mongo import MongoDBClient
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader
from pydantic import BaseModel

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


class EventDetails(BaseModel):
    department: str
    name: str
    imageUrl: str


class EventsResponse(BaseModel):
    events: List[EventDetails]


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def events_handler(
    question: str, history: list, scratch_pad: object, locations: list, nearby_locations: list
) -> EventsResponse:
    logger.info("Starting events_handler", facets={"question": question})

    rag_question = question
    if scratch_pad.get("rag"):
        rag_question = scratch_pad.get("rag")

    club_locations = locations
    if nearby_locations:
        club_locations = nearby_locations

    embedding = await OpenAIClient.generate_embedding(rag_question)
    logger.info("Generated embedding in events_handler")

    prefilter = None
    if locations:
        prefilter = {
            "clubId": {"$in": [f"{location.get('clubId')}" for location in club_locations]}
        }

    documents = MongoDBClient.vector_search(
        embedding,
        "AI.location_events_embedded",
        pre_filter=prefilter,
        post_filter=[
            {"$addFields": {"score": {"$meta": "vectorSearchScore"}}},
            {"$match": {"score": {"$gte": 0.60}}},
            {"$sort": {"score": -1}},
        ],
        k=8,
        include_score=True,
    )
    logger.info("Fetched documents in events_handler")

    if not documents["results"] or len(documents["results"]) == 0:
        logger.info("No events found in events_handler", facets={"rag_question": rag_question})
        return {"events": []}

    template = env.get_template("events.jinja2")
    system_prompt = template.render(events=documents["results"], history=history)
    response = await OpenAIClient.chat_structured(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        response_format=EventsResponse,
    )
    events = response.model_dump()
    return events

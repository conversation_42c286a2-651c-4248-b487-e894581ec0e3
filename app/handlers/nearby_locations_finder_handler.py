import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from dotenv import load_dotenv
from jinja2 import Environment, FileSystemLoader

from app.repositories.nearest_club_serverlet import get_nearby_locations

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def nearby_locations_finder_handler(scratch_pad: dict) -> any:
    load_dotenv()

    logger.info(
        "Nearby Locations Finder Handler",
        facets={"nearbyEnabled": scratch_pad.get("nearbyEnabled")},
    )

    if not scratch_pad.get("nearbyEnabled"):
        return {"nearby_locations": None}

    nearby_locations = await get_nearby_locations(
        scratch_pad.get("partyId"), scratch_pad.get("latitude"), scratch_pad.get("longitude")
    )

    locations = []
    if nearby_locations and len(nearby_locations) > 0:
        for location in nearby_locations:
            # remove work for now
            if location.get("membershipLevel") != "Work Only":
                locations.append(
                    {"clubId": location.get("mmsClubId"), "name": location.get("name")}
                )

    return {"nearby_locations": locations}

import json
import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.mongo import MongoDBClient
from ai_agent_utils.services.openai import OpenAIClient
from ai_agent_utils.services.redis import Redis
from dotenv import load_dotenv
from jinja2 import Environment, FileSystemLoader
from pydantic import BaseModel, Field

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


class Club(BaseModel):
    clubId: int = Field(
        description="ClubId of the club",
    )
    name: str = Field(
        description="Name of the club",
    )

    def get(self, attr, default=None):
        return getattr(self, attr, default)


class ClubResponse(BaseModel):
    clubs: list[Club] = Field(
        description="List of clubs",
    )

    def get(self, attr, default=None):
        return getattr(self, attr, default)


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def locations_handler(scratch_pad: dict, question: str, history: list) -> any:
    load_dotenv()

    locations = scratch_pad.get("locationNames", [])
    homeClub = scratch_pad.get("homeClub")

    logger.info("Location Finder Handler", facets={"locations": locations, "homeClub": homeClub})

    # Build a cache key based on the sorted locations array.
    # Sorting ensures that the key remains consistent regardless of order.
    sorted_locations = sorted([loc.lower().replace(" ", "_") for loc in locations])
    cache_key = f"location_finder_{'_'.join(sorted_locations)}"

    # Attempt to retrieve cached result
    cached_response = await Redis.get(cache_key)
    if cached_response:
        return cached_response

    clubs = []
    for location in locations:
        embedding = await OpenAIClient.generate_embedding(location)

        documents = MongoDBClient.vector_search(
            embeddings=embedding,
            namespace="AI.location_clubid_embedded",
            post_filter=[
                {"$addFields": {"score": {"$meta": "vectorSearchScore"}}},
                {"$match": {"score": {"$gte": 0.88}}},
                {"$sort": {"score": -1}},
            ],
            k=6,
            include_score=True,
        )

        template = env.get_template("locations.jinja2")
        system_prompt = template.render(
            context=documents["results"], history=history, user_data=scratch_pad
        )
        results = await OpenAIClient.chat_structured(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question},
            ],
            response_format=ClubResponse,
        )

        for club in results.clubs:
            club = club.model_dump(mode="json")
            if club not in clubs:
                clubs.append(club)

    if not clubs and homeClub:
        clubs.append({"clubId": homeClub, "name": scratch_pad.get("homeClubName")})

    response = {"locations": clubs}
    logger.info("Found Locations", facets={"locations": clubs})

    # Cache the response with an expiration (e.g., 14400 seconds = 4 hour)
    await Redis.set(cache_key, json.dumps(response), ttl=14400)

    return response

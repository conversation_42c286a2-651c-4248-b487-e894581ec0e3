import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader
from pydantic import BaseModel, Field

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


class IntentResponse(BaseModel):
    intent: str = Field(
        description="Intent detected in the user's question and history context",
    )
    confidence: float = Field(
        description="Confidence of the intent",
    )

    def get(self, attr, default=None):
        return getattr(self, attr, default)


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def intents_handler(question: str, history: list, scratch_pad: dict) -> dict:
    template = env.get_template("intent.jinja2")
    system_prompt = template.render(question=question, history=history)
    response = await OpenAIClient.chat_structured(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        temperature=0,
        response_format=IntentResponse,
    )

    logger.info(
        f"Intent detected: {response.intent} with confidence: {response.confidence}",
        facets={"question": question, "scratch_pad": scratch_pad},
    )

    return {"decision": response}

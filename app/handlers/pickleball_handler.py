import datetime
import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.services.launch_darkly import LaunchDarklyClient
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.models.datetime_extractor import DateTimeExtraction
from app.models.pickleball_schedule import FlattenedPickleballBooking
from app.models.reference_cards import CarouselCard, MetaData, Reference
from app.repositories.pickleball_schedule_search import search_pickleball_schedules

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def pickleball_handler(
    question: str,
    scratch_pad: dict,
    locations: list,
    nearby_locations: list,
    booking_datetime: DateTimeExtraction,
    stream: bool = False,
) -> dict:
    context = LaunchDarklyClient.get_user_context(scratch_pad)
    show_new_links = LaunchDarklyClient.client.variation("new-links-feature", context, False)

    club_locations = locations
    if nearby_locations:
        club_locations = nearby_locations

    # picklball booking schedule
    schedule_responses = await search_pickleball_schedules(club_locations, booking_datetime)

    court_schedules = []
    all_references = []
    club_ids = [loc.get("clubId") for loc in club_locations if loc.get("clubId")]
    club_map = {loc["clubId"]: loc for loc in club_locations if loc.get("clubId")}

    for schedule_response, club_id in zip(schedule_responses, club_ids):
        bookings, refs = await process_schedule(
            schedule_response, booking_datetime, club_map[club_id]
        )
        court_schedules.extend(bookings)
        all_references.extend(refs)

    template = env.get_template("pickleball_scheduling.jinja2")
    system_prompt = template.render(
        court_schedules=court_schedules,
        scratch_pad=scratch_pad,
        booking_datetime=booking_datetime.model_dump(),
        locations=locations,
    )
    response = await OpenAIClient.chat_create(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        stream=stream,
    )
    if not stream:
        response = response.choices[0].message.content

    references = []
    if show_new_links:
        references = format_pickleball_references(all_references)

    else:
        references = MetaData(
            type="basic",
            label="View Court Schedules",
            link=f"lifetime-member://courtschedules?resourcetype=pickleball&clubid={locations[0]['clubId']}",
            linkType="deeplink",
        )
    return {
        "answer": response,
        "references": references,
        "path": "pickleball_scheduling",
        "metadata": None,
    }


def convert_to_ampm(time_str: str) -> str:
    dt = datetime.datetime.fromisoformat(time_str)
    ampm_time = dt.strftime("%-I:%M%p").lower()
    return ampm_time.lstrip("0")


async def process_schedule(
    schedule_response, booking_datetime: DateTimeExtraction, club_info: dict
):
    booking_refs = []
    start_bound = datetime.datetime.strptime(booking_datetime.start_time, "%H:%M:%S").time()
    end_bound = datetime.datetime.strptime(booking_datetime.end_time, "%H:%M:%S").time()

    for court_schedule in schedule_response.schedules:
        for time_slot in court_schedule.times:
            if time_slot.state != "available":
                continue
            slot_dt = datetime.datetime.fromisoformat(time_slot.start)
            slot_time = slot_dt.time()
            if start_bound <= slot_time <= end_bound:
                ampm_time = convert_to_ampm(time_slot.start)
                booking = FlattenedPickleballBooking(
                    start=time_slot.start,
                    state=time_slot.state,
                    startSeconds=time_slot.startSeconds,
                    resourceName=court_schedule.resourceName,
                    resourceType=court_schedule.resourceType,
                    location_name=club_info.get("name", ""),
                )
                reference = {
                    "type": "basic",
                    "label": f"{court_schedule.resourceType} Pickleball {court_schedule.resourceName}, {booking_datetime.date}, {ampm_time}",
                    "link": f"lifetime-member://reservecourt?resourcetype=pickleball&clubid={club_info['clubId']}&courtid={court_schedule.id}&time={time_slot.start}",
                    "linkType": "deeplink",
                    "location_name": club_info.get("name", ""),
                }
                booking_refs.append((booking, reference))

    booking_refs.sort(key=lambda x: datetime.datetime.fromisoformat(x[0].start))
    selected = booking_refs[:8]
    court_bookings = [booking for booking, _ in selected]
    references = [ref for _, ref in selected]

    return court_bookings, references


def format_pickleball_references(all_references: list) -> list:
    carousel_cards = []
    image_url = "https://lifetime.life/content/dam/ltp/images/omsc/sky-pickleball-xl.jpg"

    # group references by location_name
    location_groups = {}
    for ref in all_references:
        location_name = ref.get("location_name", "Unknown Location")
        if location_name not in location_groups:
            location_groups[location_name] = []
        location_groups[location_name].append(ref)

    # pocess each location, limiting to 8 cards per location
    for location_name, refs in location_groups.items():
        # limit to 8 per location
        for ref in refs[:8]:
            label_parts = ref.get("label", "").split(", ")
            court_name = label_parts[0] if len(label_parts) > 0 else ""

            # Extract date and time from label parts
            date_str = label_parts[1] if len(label_parts) > 1 else ""
            time_str = label_parts[2] if len(label_parts) > 2 else ""

            # Format date as MM/DD
            formatted_date = ""
            if date_str:
                try:
                    date_obj = datetime.datetime.strptime(date_str, "%Y-%m-%d")
                    formatted_date = date_obj.strftime("%m/%d")
                except ValueError:
                    formatted_date = date_str

            # Format description as MM/DD, H:MMam/pm Location
            description = (
                f"{formatted_date}, {time_str} {location_name}"
                if formatted_date and time_str
                else f"{location_name}"
            )

            card = CarouselCard(
                component="carousel_card",
                imageUrl=image_url,
                eyebrowText=location_name,
                headlineText=court_name,
                description=description,
                link=ref.get("link", ""),
                linkType=ref.get("linkType", "deeplink"),
                cardSize="large",
                name=location_name,
            )
            carousel_cards.append(card.dict())

    references = []
    if carousel_cards:
        references = [
            Reference(
                component="carousel",
                carouselId="laic-references",
                id="laic-references",
                type="horizontal",
                title=None,
                linkText=None,
                linkUrl=None,
                linkType=None,
                children=carousel_cards,
            ).model_dump()
        ]

    return references

import json
import os
from typing import AsyncGenerator

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger  # added logger for important events
from ai_agent_utils.services.launch_darkly import LaunchDarklyClient
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.repositories.reservations_search import fetch_reservations, update_reservation_links

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def reservations_handler(
    question: str, history: list, scratch_pad: dict, booking_datetime: dict, stream=False
) -> AsyncGenerator[str, None]:
    logger.info("starting reservations handler")  # logging start of handler

    context = LaunchDarklyClient.get_user_context(scratch_pad)
    show_new_links = LaunchDarklyClient.client.variation("new-links-feature", context, False)

    # standard reservation query flow
    reservations = await fetch_reservations(scratch_pad, booking_datetime)
    template = env.get_template("reservations.jinja2")
    system_prompt = template.render(
        history=history, context=json.dumps(reservations), user_data=scratch_pad
    )

    response = await OpenAIClient.chat_create(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        stream=stream,
    )

    if not stream:
        response = response.choices[0].message.content

    references = []
    if show_new_links:
        references = update_reservation_links(reservations, new_links=True)
        if references and len(references) >= 1:
            references = [
                {
                    "component": "carousel",
                    "carouselId": "laic-references",
                    "id": "laic-references",
                    "type": "horizontal",
                    "title": None,
                    "linkText": None,
                    "linkUrl": None,
                    "linkType": None,
                    "children": references,
                }
            ]
    else:
        references = update_reservation_links(reservations)

    return {
        "answer": response,
        "references": references,
        "path": "scheduling_reservation_cancellation_faq",
    }

import os

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.launch_darkly import LaunchDarklyClient
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.models.carousel import Carousel
from app.models.datetime_extractor import DateTimeExtraction
from app.models.reference_cards import MetaData
from app.repositories.schedule_search import search_schedules

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def classes_scheduling_handler(
    question: str,
    history: list,
    scratch_pad: dict,
    locations: list,
    nearby_locations: list,
    classes: list,
    booking_datetime: DateTimeExtraction,
    stream=False,
) -> dict:
    logger.info(
        "Starting classes_scheduling_handler",
        facets={
            "question": question,
            "scratch_pad": scratch_pad,
            "booking_datetime": booking_datetime.model_dump(),
        },
    )

    club_locations = locations
    if nearby_locations:
        club_locations = nearby_locations

    class_schedules = await search_schedules(classes, club_locations, booking_datetime, scratch_pad)
    logger.info(
        "Fetched class schedules in classes_scheduling_handler",
        facets={"mapped_results": len(class_schedules.get("mapped_results", []))},
    )

    context = LaunchDarklyClient.get_user_context(scratch_pad)
    show_new_links = LaunchDarklyClient.client.variation("new-links-feature", context, False)

    template = env.get_template("classes_scheduling.jinja2")
    system_prompt = template.render(
        history=history,
        classes=class_schedules["mapped_results"],
        user_data=scratch_pad,
        booking_datetime=booking_datetime.model_dump(),
    )

    response = await OpenAIClient.chat_create(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        stream=stream,
    )

    if not stream:
        response = response.choices[0].message.content

    if show_new_links and len(class_schedules["mapped_results"]) > 0:
        references = Carousel.from_mapped_results_and_references(class_schedules["mapped_results"])

        if references and references.children:
            # carousel card with class schedules
            references = [references.model_dump()]
        else:
            # Class schedule with no cards. sub location cards instead
            location_cards = Carousel.from_locations(club_locations)
            references = [location_cards.model_dump()]
    else:
        # use old links if user is on older version
        references = [
            MetaData(**reference).model_dump() for reference in class_schedules["references"]
        ]

    return {
        "answer": response,
        "references": references,
        "path": "scheduling_class_booking",
        "metadata": None,
    }

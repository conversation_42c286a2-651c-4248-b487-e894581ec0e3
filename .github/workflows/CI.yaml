name: 'CI'

on:
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - development
      - main

env:
  ## Sets static global environment variables
  PYTHON_VERSION: '>=3.11 <3.12'
  PROJECT_NAME: 'ai-members-scheduling-agent'
  SRC_PROJECT_PATH: './'
  TEST_PROJECT_PATH: './tests'

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Use Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install uv
        run: curl -LsSf https://astral.sh/uv/install.sh | sh

      - name: Add uv to PATH
        run: echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Install dependencies using uv
        env:
          UV_INDEX_NEXUS_USERNAME: ${{ secrets.NEXUS_AUTH_USER }}
          UV_INDEX_NEXUS_PASSWORD: ${{ secrets.NEXUS_AUTH_TOKEN }}
        run: uv pip install --system -e ".[dev]"

      - name: <PERSON><PERSON>
        run: ruff check .

      - name: Pre-commit checks
        run: pre-commit run --all-files

  helm:
    uses: Life-Time-Inc/workflows-library/.github/workflows/helm-update.yml@helm-update-v1
    with:
      PROJECT_NAME: ai-members-scheduling-agent
      CHART_REPO_USERNAME: ${{ vars.NEXUS_AUTH_USER }}
      UPLOAD_ARTIFACT: true
    secrets:
      CHART_REPO_PASSWORD: ${{ secrets.NEXUS_AUTH_TOKEN }}

  docker-build:
    needs: helm
    uses: Life-Time-Inc/workflows-library/.github/workflows/docker-build.yml@docker-build-v1
    with:
      DOWNLOAD_BUILD_ARTIFACT: false
      IMAGE_NAME: ${{ github.repository }}
      REGISTRY_USERNAME: ${{ github.actor }}
      PUBLISH: true
      BUILD_ARGS: EVAL_FLOW=true
    secrets:
      REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
      SECRETS: |
        "NEXUS_AUTH_TOKEN=${{ secrets.NEXUS_AUTH_TOKEN }}"
        "NEXUS_AUTH_USER=${{ secrets.NEXUS_AUTH_USER }}"
        "NPM_TOKEN=${{ secrets.LTF_PACKAGES_TOKEN }}"

  tag-release:
    needs:
      - helm
      - docker-build
    uses: Life-Time-Inc/workflows-library/.github/workflows/github-tag-release.yml@github-tag-release-v1
    with:
      TAG: ${{ needs.docker-build.outputs.Version }}
      PRERELEASE: ${{ github.event.ref != 'refs/heads/main' }}

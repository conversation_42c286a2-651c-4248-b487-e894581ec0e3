name: 'CD'

on:
  push:
    branches:
      - main

env:
  ## Sets static global environment variables
  PROJECT_NAME: 'ai-members-scheduling-agent'
  SRC_PROJECT_PATH: './'
  TEST_PROJECT_PATH: './tests'

jobs:
  helm:
    uses: Life-Time-Inc/workflows-library/.github/workflows/helm-update.yml@helm-update-v1
    with:
      PROJECT_NAME: 'ai-members-scheduling-agent'
      CHART_REPO_USERNAME: ${{ vars.NEXUS_AUTH_USER }}
      UPLOAD_ARTIFACT: true
    secrets:
      CHART_REPO_PASSWORD: ${{ secrets.NEXUS_AUTH_TOKEN }}

  docker:
    needs: helm
    uses: Life-Time-Inc/workflows-library/.github/workflows/docker-build.yml@docker-build-v1
    with:
      DOWNLOAD_BUILD_ARTIFACT: false
      IMAGE_NAME: ${{ github.repository }}
      REGISTRY_USERNAME: ${{ github.actor }}
      PUBLISH: true
    secrets:
      REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
      SECRETS: |
        "NEXUS_AUTH_TOKEN=${{ secrets.NEXUS_AUTH_TOKEN }}"
        "NEXUS_AUTH_USER=${{ secrets.NEXUS_AUTH_USER }}"
        "NPM_TOKEN=${{ secrets.LTF_PACKAGES_TOKEN }}"

  tag-release:
    needs:
      - helm
      - docker
    uses: Life-Time-Inc/workflows-library/.github/workflows/github-tag-release.yml@github-tag-release-v1
    with:
      TAG: ${{ needs.docker.outputs.Version }}
      PRERELEASE: ${{ github.event.ref != 'refs/heads/main' }}

  QA-Deployment:
    needs:
      - docker
      - tag-release
    uses: Life-Time-Inc/workflows-library/.github/workflows/aks-deploy-dotnet.yml@aks-deploy-dotnet-v3
    with:
      AZURE_CREDENTIALS_SECRET_NAME: LTT_FLEX_QA
      ENV_NAME: QA
      ENV_URL: https://qa-api.lifetime.life/ai/members/chats
      HELM_RELEASE_NAME: ai-members-scheduling-agent
      HELM_OVERRIDE_FILE: values-qa.yaml
      DEPLOYMENT_VERSION: ${{ needs.docker.outputs.Version }}
      AZURE_SUBSCRIPTION: nonprod-applications
      CLUSTER_RESOURCE_GROUP: qa-shared-aks-rg
      CLUSTER_NAMESPACE: ai
      DEPLOY_TIMEOUT: 8
    secrets: inherit

  Production-Deployment:
    needs:
      - docker
      - tag-release
    uses: Life-Time-Inc/workflows-library/.github/workflows/aks-deploy-dotnet.yml@aks-deploy-dotnet-v3
    with:
      AZURE_CREDENTIALS_SECRET_NAME: LTT_FLEX_PRD
      ENV_NAME: Production
      ENV_URL: https://api.lifetime.life/ai/members/chats
      HELM_OVERRIDE_FILE: values-prod.yaml
      HELM_RELEASE_NAME: ai-members-scheduling-agent
      DEPLOYMENT_VERSION: ${{ needs.docker.outputs.Version }}
      AZURE_SUBSCRIPTION: prd-applications
      CLUSTER_RESOURCE_GROUP: prd-shared-aks-rg
      CLUSTER_NAMESPACE: ai
      DEPLOY_TIMEOUT: 8
    secrets: inherit

[pytest]
pythonpath = ./app
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v
asyncio_mode = strict
asyncio_default_fixture_loop_scope = function
markers =
    asyncio: mark a test as an asyncio test
    integration: mark a test as an integration test (minimal mocking, real API calls)
    component: mark a test as a component test (API contracts, some mocking)
    unit: mark a test as a unit test (heavy mocking, isolated functions)

FROM python:3.11-slim

ARG EVAL_FLOW
ENV EVAL_FLOW=${EVAL_FLOW}
ENV DD_TRACE_HTTPLIB_ENABLED=true
ENV PYTHONUNBUFFERED=1

WORKDIR /app
# Make both /app and /app/app discoverable
ENV PYTHONPATH=/app:/app/app

# Install build dependencies and uv
RUN apt-get update && \
    apt-get install -y --no-install-recommends gcc curl && \
    rm -rf /var/lib/apt/lists/* && \
    curl -LsSf https://astral.sh/uv/install.sh | sh && \
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc

# Add uv to PATH for subsequent RUN commands
ENV PATH="/root/.local/bin:$PATH"

# Copy pyproject.toml and uv.lock first for better caching
COPY pyproject.toml uv.lock ./
COPY app ./app
COPY README.md ./

# Install dependencies using uv
RUN --mount=type=secret,id=NEXUS_AUTH_USER \
    --mount=type=secret,id=NEXUS_AUTH_TOKEN \
    export UV_INDEX_NEXUS_USERNAME=$(cat /run/secrets/NEXUS_AUTH_USER) && \
    export UV_INDEX_NEXUS_PASSWORD=$(cat /run/secrets/NEXUS_AUTH_TOKEN) && \
    uv pip install --system --no-cache-dir -e .

ENV DD_TRACE_REQUESTS_EXCLUDED_URLS=".*telemetry/proxy/api/v2/apmtelemetry.*,.*v0.7/config.*"

EXPOSE 3000
CMD ["ddtrace-run", "gunicorn", "-k", "uvicorn.workers.UvicornWorker", "-w", "8", "--threads", "1", "--bind", "0.0.0.0:3000", "--timeout", "600", "app.main:app"]

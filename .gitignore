# python
.env
__pycache__/
.runs/
/venv
flow/venv/*
/run/*
/secrets/
.secrets/

# Virtual environment
.venv/

# promptflow
.promptflow/*
!.promptflow/flow.tools.json
flow.log
flow.detail.json
flow.output.json

# IDE specifics
.idea
.vscode

# Linting and formatting
.ruff_cache
.pre-commit-cache

# Mac files
.DS_Store

.python-version

# local files
temp.jinja2
temp.json
swagger.json
swagger2.json
client_details.py
temp2.jinja2

# Testing
.pytest_cache/
**/.pytest_cache/
